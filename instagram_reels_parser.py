"""
Instagram Reels Parser with GUI
Парсер Instagram Reels с графическим интерфейсом

Автор: AI Assistant
Дата: 2024
Версия: 1.0

Требования:
- Python 3.9+
- Windows 11
- HikerAPI ключ
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
import pandas as pd
import requests
import re
import schedule
import time
from datetime import datetime
import threading
import os
import subprocess
import configparser
import logging
import traceback
from pathlib import Path
import json
from typing import Dict, List, Optional, Tuple


class ToggleSwitch(tk.Canvas):
    def __init__(self, master=None, width=50, height=24, on_toggle=None, initial=False, **kwargs):
        super().__init__(master, width=width, height=height, highlightthickness=0, bd=0, **kwargs)
        self.width = width
        self.height = height
        self.on_toggle = on_toggle
        self.state = initial
        self._draw()
        self.bind('<Button-1>', self._toggle)
        self.bind('<Enter>', lambda e: self.config(cursor='hand2'))
        self.bind('<Leave>', lambda e: self.config(cursor=''))

    def _draw(self):
        self.delete('all')
        # Background
        if self.state:
            bg_color = '#4caf50'
            circle_color = '#fff'
        else:
            bg_color = '#cccccc'
            circle_color = '#fff'
        self.create_rounded_rect(2, 2, self.width-2, self.height-2, radius=self.height//2, fill=bg_color, outline=bg_color)
        # Circle
        offset = self.width - self.height if self.state else 0
        self.create_oval(2+offset, 2, 2+self.height-4+offset, self.height-2, fill=circle_color, outline=bg_color)

    def _toggle(self, event=None):
        self.state = not self.state
        self._draw()
        if self.on_toggle:
            self.on_toggle(self.state)

    def set(self, value: bool):
        self.state = value
        self._draw()

    def get(self):
        return self.state

    def create_rounded_rect(self, x1, y1, x2, y2, radius=10, **kwargs):
        points = [
            x1+radius, y1,
            x2-radius, y1,
            x2, y1,
            x2, y1+radius,
            x2, y2-radius,
            x2, y2,
            x2-radius, y2,
            x1+radius, y2,
            x1, y2,
            x1, y2-radius,
            x1, y1+radius,
            x1, y1
        ]
        return self.create_polygon(points, smooth=True, **kwargs)


class InstagramReelsParser:
    """Основной класс для парсинга Instagram Reels"""
    
    def __init__(self):
        self.config = self._load_config()
        self._setup_logging()
        self.is_parsing = False
        self.scheduler_thread = None
        self.stats = {"success": 0, "errors": 0, "total": 0}
        
    def _load_config(self):
        """Загрузка конфигурации из файла"""
        config_path = Path('config.ini')
        config = configparser.ConfigParser()
        
        # Создаем дефолтный конфиг, если файл не существует
        if not config_path.exists():
            self._create_default_config(config_path)
            
        # Пробуем разные кодировки для чтения конфига
        encodings = ['utf-8', 'cp1251', 'utf-8-sig']
        for encoding in encodings:
            try:
                with open(config_path, 'r', encoding=encoding) as f:
                    config.read_file(f)
                # Если дошли сюда, значит чтение прошло успешно
                return config
            except UnicodeDecodeError:
                if encoding == encodings[-1]:  # Если это последняя кодировка
                    print(f"Не удалось прочитать конфиг ни в одной из кодировок: {encodings}")
                    # Создаем новый конфиг и возвращаем его
                    self._create_default_config(config_path)
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config.read_file(f)
                    return config
                continue
            except Exception as e:
                print(f"Ошибка при чтении конфига: {e}")
                # В случае других ошибок создаем новый конфиг
                self._create_default_config(config_path)
                with open(config_path, 'r', encoding='utf-8') as f:
                    config.read_file(f)
                return config
                
        return config  # На всякий случай, если что-то пошло не так
    
    def _create_default_config(self, config_path: Path):
        """Создание дефолтного конфигурационного файла"""
        default_config = """[API]
hiker_api_key = YOUR_HIKER_API_KEY_HERE
base_url = https://hikerapi.com/api

[SETTINGS]
daily_run_time = 09:00
request_delay = 2
max_retries = 3
request_timeout = 30

[FILES]
url_column = Ссылка на Reels
account_column = Account_Name
output_suffix = _stats"""
        
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(default_config)
    
    def _setup_logging(self):
        """Настройка логирования"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('instagram_parser.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def extract_shortcode(self, url: str) -> Optional[str]:
        """Извлечение shortcode из URL Instagram Reel"""
        patterns = [
            r'reel/([A-Za-z0-9_-]+)',
            r'reels/([A-Za-z0-9_-]+)',
            r'p/([A-Za-z0-9_-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def fetch_reel_data(self, shortcode: str) -> Dict:
        """Получение данных о Reel через HikerAPI v2"""
        try:
            # Получаем настройки из конфига
            access_key = self.config.get('API', 'access_key')
            base_url = self.config.get('API', 'base_url')
            timeout = self.config.getint('SETTINGS', 'request_timeout', fallback=30)
            max_retries = self.config.getint('SETTINGS', 'max_retries', fallback=3)
            debug_mode = self.config.getboolean('API', 'debug', fallback=False)
            
            # Проверяем API ключ
            if not access_key or access_key == "YOUR_ACCESS_KEY_HERE":
                error_msg = "API access key не настроен в config.ini"
                self.logger.error(error_msg)
                return self._create_error_response(error_msg)
            
            # Настраиваем заголовки запроса для HikerAPI v2
            headers = {
                "accept": "application/json",
                "x-access-key": access_key,
                "User-Agent": "Instagram-Reels-Parser/1.0"
            }
            
            # Эндпоинты для HikerAPI v2
            # Пробуем разные варианты эндпоинтов для получения данных о посте
            endpoints = [
                (f"{base_url}/instagram/media/by/shortcode", "GET", {"shortcode": shortcode}),  # Основной эндпоинт
                (f"{base_url}/instagram/media/info", "GET", {"shortcode": shortcode}),  # Альтернативный
                (f"{base_url}/instagram/post/by/shortcode", "GET", {"shortcode": shortcode})  # Еще один вариант
            ]
            
            # Пробуем сделать запрос с несколькими попытками и разными эндпоинтами
            for endpoint, method, params in endpoints:
                if debug_mode:
                    self.logger.info(f"Запрос к API: {endpoint}?shortcode={shortcode}")
                try:
                    if debug_mode:
                        self.logger.info(f"Попытка запроса к эндпоинту: {endpoint}")
                    
                    if method == "GET":
                        response = requests.get(
                            endpoint, 
                            headers=headers, 
                            params=params,
                            timeout=timeout
                        )
                    else:
                        response = requests.post(
                            endpoint,
                            headers=headers,
                            json=params,
                            timeout=timeout
                        )
                    
                    if debug_mode:
                        self.logger.debug(f"Статус ответа: {response.status_code}")
                        self.logger.debug(f"Заголовки ответа: {dict(response.headers)}")
                        self.logger.debug(f"Тело ответа: {response.text[:500]}")
                    
                    # Обрабатываем успешный ответ
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            return self._parse_api_response_v2(data)
                        except json.JSONDecodeError as je:
                            error_msg = f"Ошибка декодирования JSON: {je}"
                            self.logger.error(f"{error_msg}. Ответ: {response.text[:200]}")
                            return self._create_error_response(error_msg)
                    
                    # Обрабатываем ошибки
                    elif response.status_code == 401:
                        error_msg = "Ошибка авторизации: неверный API access key"
                        self.logger.error(error_msg)
                        return self._create_error_response(error_msg)
                        
                    elif response.status_code == 404:
                        error_msg = f"Reel {shortcode} не найден (404)"
                        self.logger.warning(error_msg)
                        return self._create_error_response(error_msg)
                        
                    elif response.status_code == 429:
                        wait_time = 60  # Ждем 60 секунд при превышении лимитов
                        self.logger.warning(f"Превышен лимит запросов. Ожидание {wait_time} сек...")
                        time.sleep(wait_time)
                        continue
                        
                    else:
                        error_msg = f"Ошибка API ({response.status_code}): {response.text[:200]}"
                        self.logger.error(error_msg)
                        
                except requests.RequestException as e:
                    error_msg = f"Ошибка запроса: {str(e)}"
                    self.logger.error(error_msg)
                
                # Экспоненциальная задержка между попытками
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 2  # 2, 4, 8 секунд
                    self.logger.info(f"Повторная попытка через {wait_time} сек...")
                    time.sleep(wait_time)
            
            # Если все попытки исчерпаны
            error_msg = f"Не удалось получить данные после {max_retries} попыток"
            self.logger.error(error_msg)
            return self._create_error_response(error_msg)
            
        except Exception as e:
            error_msg = f"Непредвиденная ошибка: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return self._create_error_response(error_msg)
    
    def _parse_api_response_v2(self, data: Dict) -> Dict:
        """Парсинг ответа API v2 в унифицированный формат"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Пробуем разные структуры ответа в зависимости от API
        possible_structures = [
            # Структура 1: стандартная Instagram API
            {
                "views": ["view_count", "play_count", "video_view_count"],
                "likes": ["like_count", "likes", "edge_media_preview_like.count"],
                "comments": ["comment_count", "comments", "edge_media_to_comment.count"]
            },
            # Структура 2: альтернативная структура
            {
                "views": ["statistics.view_count", "metrics.views"],
                "likes": ["statistics.like_count", "metrics.likes"],
                "comments": ["statistics.comment_count", "metrics.comments"]
            }
        ]
        
        result = {"views": 0, "likes": 0, "comments": 0, "timestamp": timestamp}
        
        for structure in possible_structures:
            for metric, paths in structure.items():
                for path in paths:
                    value = self._get_nested_value(data, path)
                    if value is not None:
                        result[metric] = value
                        break
                if result[metric] > 0:
                    break
        
        return result
    
    def _create_error_response(self, error_message: str) -> Dict:
        """Создание стандартного ответа с ошибкой"""
        return {
            "error": error_message,
            "views": 0,
            "likes": 0,
            "comments": 0,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
    def _get_nested_value(self, data: Dict, path: str):
        """Получение значения из вложенной структуры по пути"""
        try:
            keys = path.split('.')
            value = data
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            return int(value) if isinstance(value, (int, float, str)) and str(value).isdigit() else None
        except:
            return None
    
    def validate_excel_file(self, file_path: str) -> Tuple[bool, str]:
        """Валидация Excel файла"""
        try:
            if not os.path.exists(file_path):
                return False, f"Файл не найден: {file_path}"
            
            df = pd.read_excel(file_path)
            url_column = self.config.get('FILES', 'url_column')
            
            if url_column not in df.columns:
                return False, f"Столбец '{url_column}' не найден. Доступные столбцы: {list(df.columns)}"
            
            if df.empty:
                return False, "Excel файл пустой"
            
            # Проверка валидности URL
            valid_urls = 0
            for url in df[url_column].dropna():
                if self.extract_shortcode(str(url)):
                    valid_urls += 1
            
            if valid_urls == 0:
                return False, "Не найдено валидных Instagram Reels URL"
            
            return True, f"Файл валидный. Найдено {valid_urls} валидных URL из {len(df)}"
            
        except Exception as e:
            return False, f"Ошибка при чтении файла: {str(e)}"
    
    def parse_and_save(self, excel_path: str, progress_callback=None, log_callback=None) -> bool:
        """Основная функция парсинга и обновления данных в том же файле"""
        if self.is_parsing:
            if log_callback:
                log_callback("Парсинг уже выполняется!")
            return False
        
        self.is_parsing = True
        self.stats = {"success": 0, "errors": 0, "total": 0}
        
        try:
            # Валидация файла
            is_valid, message = self.validate_excel_file(excel_path)
            if not is_valid:
                if log_callback:
                    log_callback(f"Ошибка валидации: {message}")
                return False
            
            if log_callback:
                log_callback(f"Файл валидный: {message}")
            
            # Чтение Excel
            df = pd.read_excel(excel_path)
            url_column = self.config.get('FILES', 'url_column', fallback='Ссылка на Reels')
            request_delay = self.config.getfloat('SETTINGS', 'request_delay')
            
            # Определение текущего дня месяца для колонки
            current_day = datetime.now().day
            day_column = str(current_day)
            
            if log_callback:
                log_callback(f"📅 Сегодня {current_day} число, буду записывать в колонку '{day_column}'")
            
            # Проверка наличия колонки для текущего дня
            if day_column not in df.columns:
                # Создаем колонку если её нет
                df[day_column] = 0
                if log_callback:
                    log_callback(f"➕ Создана новая колонка '{day_column}'")
            
            # Поиск строк с валидными URL
            valid_indices = []
            for index, row in df.iterrows():
                url = str(row.get(url_column, ""))
                if pd.notna(url) and self.extract_shortcode(url):
                    valid_indices.append(index)
            
            self.stats["total"] = len(valid_indices)
            
            if log_callback:
                log_callback(f"🎯 Найдено {self.stats['total']} валидных Reels для парсинга")
            
            # Парсинг каждого Reel
            for i, index in enumerate(valid_indices):
                if not self.is_parsing:  # Проверка на остановку
                    break
                
                url = str(df.loc[index, url_column])
                shortcode = self.extract_shortcode(url)
                
                if log_callback:
                    log_callback(f"[{i+1}/{self.stats['total']}] Парсинг: {url}")
                
                # Получение данных о просмотрах
                data = self.fetch_reel_data(shortcode)
                views = data.get("views", 0)
                
                if "error" in data and views == 0:
                    self.stats["errors"] += 1
                    if log_callback:
                        log_callback(f"❌ Ошибка: {data.get('error', 'Неизвестная ошибка')}")
                    # Записываем 0 в случае ошибки
                    df.loc[index, day_column] = 0
                else:
                    self.stats["success"] += 1
                    # Записываем количество просмотров в колонку текущего дня
                    df.loc[index, day_column] = views
                    if log_callback:
                        log_callback(f"✅ Просмотров: {views:,}")
                
                # Обновление прогресса
                if progress_callback:
                    progress = ((i + 1) / self.stats["total"]) * 100
                    progress_callback(progress)
                
                # Задержка между запросами
                time.sleep(request_delay)
            
            # Сохранение обновленного файла
            if self.stats["total"] > 0:
                # Закрываем файл, если он открыт в Excel
                try:
                    # Пробуем сохранить во временный файл
                    temp_path = excel_path.replace(".xlsx", "_temp.xlsx")
                    df.to_excel(temp_path, index=False)
                    
                    # Если сохранение во временный файл успешно, создаем бэкап и заменяем оригинальный
                    if os.path.exists(temp_path):
                        # Создаем резервную копию
                        backup_path = excel_path.replace(".xlsx", f"_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        try:
                            if os.path.exists(excel_path):
                                import shutil
                                shutil.copy2(excel_path, backup_path)
                        except Exception as e:
                            self.logger.error(f"Не удалось создать бэкап: {e}")
                        
                        # Заменяем оригинальный файл
                        try:
                            if os.path.exists(excel_path):
                                os.remove(excel_path)
                            os.rename(temp_path, excel_path)
                        except Exception as e:
                            error_msg = f"Не удалось сохранить файл. Возможно, он открыт в другой программе: {e}"
                            self.logger.error(error_msg)
                            if log_callback:
                                log_callback(error_msg)
                            return False
                except Exception as e:
                    error_msg = f"Ошибка при сохранении файла: {e}"
                    self.logger.error(error_msg)
                    if log_callback:
                        log_callback(error_msg)
                    return False
                
                if log_callback:
                    log_callback(f"✅ Парсинг завершен!")
                    log_callback(f"📊 Статистика: Успешно: {self.stats['success']}, Ошибок: {self.stats['errors']}")
                    log_callback(f"💾 Данные обновлены в файле: {excel_path}")
                    log_callback(f"🛡️ Резервная копия: {backup_path}")
                
                self.logger.info(f"Парсинг завершен. Обработано: {self.stats['total']}, Успешно: {self.stats['success']}")
            
            return True
            
        except Exception as e:
            error_msg = f"Критическая ошибка при парсинге: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return False
        
        finally:
            self.is_parsing = False
    
    def stop_parsing(self):
        """Остановка парсинга"""
        self.is_parsing = False
    
    def schedule_daily_parsing(self, excel_path: str, log_callback=None) -> bool:
        """Запуск ежедневного парсинга"""
        try:
            daily_time = self.config.get('SETTINGS', 'daily_run_time')
            
            def scheduled_parse():
                if log_callback:
                    log_callback(f"🕒 Запуск запланированного парсинга в {datetime.now().strftime('%H:%M:%S')}")
                self.parse_and_save(excel_path, log_callback=log_callback)
            
            schedule.clear()  # Очищаем предыдущие задачи
            schedule.every().day.at(daily_time).do(scheduled_parse)
            
            def run_scheduler():
                while True:
                    schedule.run_pending()
                    time.sleep(60)
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                if log_callback:
                    log_callback("Останавливаю предыдущий планировщик...")
            
            self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            if log_callback:
                log_callback(f"⏰ Ежедневный парсинг запланирован на {daily_time}")
            
            return True
            
        except Exception as e:
            if log_callback:
                log_callback(f"Ошибка планирования: {str(e)}")
            return False


class InstagramReelsGUI:
    """Графический интерфейс для парсера Instagram Reels"""
    
    def __init__(self):
        self.parser = InstagramReelsParser()
        self.root = tk.Tk()
        self.parsing_thread = None
        self.last_file_path = self._load_last_file_path()
        self.status_var = tk.StringVar(value="Готов к работе")
        self.file_path_var = tk.StringVar(value=self.last_file_path)
        self.setup_gui()
        
    def _load_last_file_path(self) -> str:
        """Загружает последний открытый файл из конфига"""
        try:
            config = configparser.ConfigParser()
            config_path = Path('config.ini')
            if not config_path.exists():
                return ""
                
            # Пробуем разные кодировки для чтения
            encodings = ['utf-8', 'utf-8-sig', 'cp1251']
            for encoding in encodings:
                try:
                    with open(config_path, 'r', encoding=encoding) as f:
                        config.read_file(f)
                    
                    if 'GUI' in config and 'last_file' in config['GUI']:
                        last_file = config['GUI']['last_file']
                        if last_file and os.path.exists(last_file):
                            return last_file
                    return ""  # Возвращаем пустую строку, если файл не найден или невалиден
                        
                except UnicodeDecodeError:
                    if encoding == encodings[-1]:  # Если это последняя кодировка
                        print(f"Не удалось прочитать конфиг ни в одной из кодировок: {encodings}")
                        # Создаем новый конфиг с дефолтными настройками
                        self._create_default_config(config_path)
                        return ""
                    continue
                except Exception as e:
                        print(f"Ошибка при загрузке последнего файла: {e}")
                        break
        except Exception as e:
            print(f"Критическая ошибка при загрузке последнего файла: {e}")
            
        return ""  # Возвращаем пустую строку, если не удалось загрузить
        
    def _save_last_file_path(self, file_path: str):
        """Сохраняет путь к последнему открытому файлу в конфиг"""
        try:
            config = self.parser._load_config()
            config_path = Path('config.ini')
            
            # Обновляем или создаем секцию GUI
            if 'GUI' not in config:
                config['GUI'] = {}
                
            config['GUI']['last_file'] = file_path
            
            # Сохраняем обратно в файл с обработкой ошибок доступа
            try:
                # Создаем временный файл
                temp_path = str(config_path) + '.tmp'
                with open(temp_path, 'w', encoding='utf-8') as configfile:
                    config.write(configfile, space_around_delimiters=True)
                
                # Заменяем старый файл новым
                if os.path.exists(config_path):
                    backup_path = str(config_path) + '.bak'
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    os.rename(config_path, backup_path)
                
                os.rename(temp_path, config_path)
                
            except PermissionError as e:
                print(f"Ошибка доступа к файлу {config_path}: {e}")
                print("Попробуйте закрыть файл, если он открыт в другой программе.")
                return False
            except Exception as e:
                print(f"Ошибка при сохранении конфига: {e}")
                return False
                
            return True
            
        except Exception as e:
            print(f"Ошибка при сохранении последнего файла: {e}")
            return False
        
    def _create_default_config(self, config_path: Path):
        """Создает конфиг с настройками по умолчанию"""
        config = configparser.ConfigParser()
        config['HIKER_API'] = {
            'base_url': 'https://hikerapi.com/v2',
            'api_key': 'your_api_key_here'
        }
        config['SETTINGS'] = {
            'daily_run_time': '09:00',
            'max_retries': '3',
            'request_timeout': '30'
        }
        config['GUI'] = {
            'last_file': ''
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                config.write(f)
        except Exception as e:
            print(f"Ошибка при создании конфига: {e}")
    
    def _validate_file(self, file_path: str):
        """Валидирует файл и обновляет статус"""
        if not file_path or not os.path.exists(file_path):
            self.status_var.set("Файл не найден")
            return
            
        is_valid, message = self.parser.validate_excel_file(file_path)
        if is_valid:
            self.log_message(f"✅ {message}")
            self.status_var.set("Файл готов к обработке")
        else:
            self.log_message(f"❌ {message}")
            self.status_var.set("Ошибка в файле")
    
    def setup_gui(self):
        """Настройка графического интерфейса"""
        self.root.title("Instagram Reels Parser v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Настройка стиля
        style = ttk.Style()
        style.theme_use('clam')
        
        # Основной фрейм
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Конфигурация веса для растягивания
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)  # Для лог-области
        
        # Заголовок
        title_label = ttk.Label(main_frame, text="Instagram Reels Parser", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Выбор файла
        ttk.Label(main_frame, text="Excel файл с ссылками:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.file_path_var = tk.StringVar(value=self.last_file_path)
        file_entry = ttk.Entry(main_frame, textvariable=self.file_path_var, width=50)
        file_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=5)
        
        ttk.Button(main_frame, text="Выбрать файл",
                  command=self.select_file).grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # --- Панель автозапуска ---
        autostart_frame = ttk.LabelFrame(main_frame, text="Автоматизация (Windows)", padding="8")
        autostart_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        autostart_frame.columnconfigure(3, weight=1)

        # Индикатор статуса
        self.autostart_status_color = tk.Canvas(autostart_frame, width=16, height=16, highlightthickness=0)
        self.autostart_status_color.grid(row=0, column=0, padx=(0, 4))
        self.autostart_status_circle = self.autostart_status_color.create_oval(3, 3, 13, 13, fill="#cccccc", outline="")

        self.autostart_status_text = ttk.Label(autostart_frame, text="Автозапуск не настроен", font=("Arial", 10, "bold"))
        self.autostart_status_text.grid(row=0, column=1, sticky=tk.W, padx=(0, 12))

        # Toggle switch для автозапуска
        self.autostart_var = tk.BooleanVar()
        self.autostart_toggle = ToggleSwitch(
            autostart_frame,
            width=50,
            height=24,
            on_toggle=self.toggle_autostart,
            initial=False
        )
        self.autostart_toggle.grid(row=0, column=2, padx=10)

        # --- Основные кнопки ---
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=5)

        self.parse_button = ttk.Button(button_frame, text="Запустить парсинг", command=self.start_parsing)
        self.parse_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="Остановить", command=self.stop_parsing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Настройки", command=self.open_settings).pack(side=tk.LEFT, padx=5)

        # ---
        # Прогресс бар
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # Лог область
        log_frame = ttk.LabelFrame(main_frame, text="Лог выполнения", padding="5")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Статус бар
        self.status_var = tk.StringVar()
        self.status_var.set("Готов к работе")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # После инициализации панели автозапуска сразу обновляем статус и положение переключателя
        self.update_autostart_status()
        self.sync_autostart_toggle()
        
        # Приветственное сообщение
        self.log_message("🚀 Instagram Reels Parser запущен")
        self.log_message("📋 Выберите Excel файл с колонкой 'Ссылка на Reels' для начала работы")
        self.log_message("⚙️ Не забудьте настроить API ключ в config.ini")
        self.log_message("🔧 Используйте кнопки 'Включить/Выключить автозапуск' для управления ежедневным парсингом")

        # Если есть последний открытый файл, валидируем его
        if self.last_file_path:
            self.log_message(f"📁 Загружен последний открытый файл: {self.last_file_path}")
            is_valid, message = self.parser.validate_excel_file(self.last_file_path)
            if is_valid:
                self.log_message(f"✅ {message}")
                self.status_var.set("Файл готов к обработке")
            else:
                self.log_message(f"❌ {message}")
                self.status_var.set("Ошибка в файле")
    
    def select_file(self):
        """Выбор Excel файла"""
        initial_dir = os.path.dirname(self.file_path_var.get()) if self.file_path_var.get() else None
        file_path = filedialog.askopenfilename(
            title="Выберите Excel файл",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
            initialdir=initial_dir
        )
        if file_path:
            self.file_path_var.set(file_path)
            self._save_last_file_path(file_path)  # Сохраняем путь к файлу
            self.log_message(f"📁 Выбран файл: {file_path}")
            
            # Валидация файла
            is_valid, message = self.parser.validate_excel_file(file_path)
            if is_valid:
                self.log_message(f"✅ {message}")
                self.status_var.set("Файл готов к обработке")
            else:
                self.log_message(f"❌ {message}")
                self.status_var.set("Ошибка в файле")
    
    def start_parsing(self):
        """Запуск парсинга в отдельном потоке"""
        excel_path = self.file_path_var.get()
        if not excel_path:
            messagebox.showerror("Ошибка", "Выберите Excel файл")
            return
        
        self.parse_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.status_var.set("Выполняется парсинг...")
        
        def parse_worker():
            try:
                success = self.parser.parse_and_save(
                    excel_path, 
                    progress_callback=self.update_progress,
                    log_callback=self.log_message
                )
                
                # Обновление UI в главном потоке
                self.root.after(0, self.parsing_finished, success)
                
            except Exception as e:
                self.root.after(0, self.log_message, f"Критическая ошибка: {str(e)}")
                self.root.after(0, self.parsing_finished, False)
        
        self.parsing_thread = threading.Thread(target=parse_worker, daemon=True)
        self.parsing_thread.start()
    
    def stop_parsing(self):
        """Остановка парсинга"""
        self.parser.stop_parsing()
        self.log_message("🛑 Запрос на остановку парсинга отправлен...")
        self.status_var.set("Остановка парсинга...")
    
    def parsing_finished(self, success: bool):
        """Завершение парсинга"""
        self.parse_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        if success:
            self.progress_var.set(100)
            self.status_var.set("Парсинг завершен успешно")
        else:
            self.status_var.set("Парсинг завершен с ошибками")
    
    def sync_autostart_toggle(self):
        """Синхронизирует положение переключателя с реальным статусом автозапуска"""
        try:
            import subprocess
            cmd = [
                "powershell.exe",
                "-Command",
                "Get-ScheduledTask -TaskName 'Instagram_Reels_Parser_Daily' -ErrorAction SilentlyContinue | Select-Object State"
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            state = None
            if result.returncode == 0 and result.stdout:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    state = lines[1].strip()
            self.autostart_var.set(state == "Ready")
        except Exception:
            self.autostart_var.set(False)

    def toggle_autostart(self, state: bool):
        """Включает или выключает автозапуск через Task Scheduler"""
        if state:
            # Мгновенно показать статус "Включение..."
            self.autostart_status_color.itemconfig(self.autostart_status_circle, fill="#ffc107")  # жёлтый
            self.autostart_status_text.config(text="Включение автозапуска...")
            self.root.after(100, self.enable_windows_autostart)
        else:
            # Мгновенно показать статус "Отключение..."
            self.autostart_status_color.itemconfig(self.autostart_status_circle, fill="#ffc107")  # жёлтый
            self.autostart_status_text.config(text="Отключение автозапуска...")
            self.root.after(100, self.disable_windows_autostart)

    def enable_windows_autostart(self):
        excel_path = self.file_path_var.get()
        if not excel_path:
            messagebox.showerror("Ошибка", "Сначала выберите Excel файл для автозапуска")
            return
        try:
            current_dir = os.getcwd()
            setup_script = os.path.join(current_dir, "setup_simple.ps1")
            if not os.path.exists(setup_script):
                messagebox.showerror("Ошибка", "Файл setup_simple.ps1 не найден!")
                return
            self.log_message("🔄 Настройка автозапуска Windows...")
            self.status_var.set("Настройка автозапуска...")
            cmd = ["powershell.exe", "-ExecutionPolicy", "Bypass", "-File", setup_script]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=current_dir)
            if result.returncode == 0:
                self.log_message("✅ Автозапуск Windows успешно включен!")
                self.log_message("📅 Задача будет запускаться каждый день в 09:00")
                self.status_var.set("Автозапуск включен")
                messagebox.showinfo("Успех", "Автозапуск успешно настроен!\n\nЗадача будет выполняться каждый день в 09:00")
            else:
                error_msg = result.stderr or "Неизвестная ошибка"
                self.log_message(f"❌ Ошибка настройки автозапуска: {error_msg}")
                self.status_var.set("Ошибка настройки автозапуска")
                messagebox.showerror("Ошибка", f"Не удалось настроить автозапуск:\n{error_msg}\n\nЗапустите программу от имени администратора")
        except Exception as e:
            self.log_message(f"❌ Критическая ошибка: {str(e)}")
            messagebox.showerror("Ошибка", f"Критическая ошибка:\n{str(e)}")
        self.update_autostart_status()

    def disable_windows_autostart(self):
        try:
            self.log_message("🔄 Отключение автозапуска Windows...")
            self.status_var.set("Отключение автозапуска...")
            cmd = ["powershell.exe", "-Command", "Unregister-ScheduledTask -TaskName 'Instagram_Reels_Parser_Daily' -Confirm:$false"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_message("✅ Автозапуск Windows успешно отключен!")
                self.status_var.set("Автозапуск отключен")
                messagebox.showinfo("Успех", "Автозапуск успешно отключен!")
            else:
                if "No MSFT_ScheduledTask objects found" in result.stderr:
                    self.log_message("ℹ️ Задача автозапуска не найдена (уже отключена)")
                    self.status_var.set("Автозапуск не активен")
                    messagebox.showinfo("Информация", "Автозапуск уже отключен или не был настроен")
                else:
                    error_msg = result.stderr or "Неизвестная ошибка"
                    self.log_message(f"❌ Ошибка отключения автозапуска: {error_msg}")
                    self.status_var.set("Ошибка отключения")
                    messagebox.showerror("Ошибка", f"Не удалось отключить автозапуск:\n{error_msg}")
        except Exception as e:
            self.log_message(f"❌ Критическая ошибка: {str(e)}")
            messagebox.showerror("Ошибка", f"Критическая ошибка:\n{str(e)}")
        self.update_autostart_status()

    def update_autostart_status(self):
        """Обновляет цвет и текст статуса автозапуска"""
        try:
            import subprocess
            cmd = [
                "powershell.exe",
                "-Command",
                "Get-ScheduledTask -TaskName 'Instagram_Reels_Parser_Daily' -ErrorAction SilentlyContinue | Select-Object State"
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            self.log_message(f"[DEBUG] PowerShell stdout: {result.stdout.strip()}")
            self.log_message(f"[DEBUG] PowerShell stderr: {result.stderr.strip()}")
            state = None
            if result.returncode == 0 and result.stdout:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    state = lines[1].strip().lower()
            if state and ("ready" in state or "готово" in state):
                color = "#4caf50"  # зелёный
                text = "Работает: ежедневный автопарсинг!"
            elif state and ("running" in state or "выполняется" in state):
                color = "#ffc107"  # жёлтый
                text = "Автозапуск выполняется"
            elif state and ("disabled" in state or "отключено" in state):
                color = "#f44336"  # красный
                text = "Автозапуск ОТКЛЮЧЕН"
            elif state:
                color = "#cccccc"
                text = f"Автозапуск: {state}"
            else:
                color = "#cccccc"
                text = "Автозапуск не настроен (задача не найдена)"
                self.log_message("[DEBUG] Задача автозапуска не найдена")
            self.autostart_status_color.itemconfig(self.autostart_status_circle, fill=color)
            self.autostart_status_text.config(text=text)
        except Exception as e:
            self.autostart_status_color.itemconfig(self.autostart_status_circle, fill="#cccccc")
            self.autostart_status_text.config(text="Статус: неизвестно")
            self.log_message(f"[DEBUG] Ошибка проверки статуса автозапуска: {e}")

    def open_settings(self):
        """Открытие окна настроек"""
        SettingsWindow(self.root, self.parser.config)
    
    def update_progress(self, value: float):
        """Обновление прогресса"""
        self.progress_var.set(value)
    
    def log_message(self, message: str):
        """Добавление сообщения в лог"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Безопасное обновление из любого потока
        def update_log():
            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
    
    def run(self):
        """Запуск GUI"""
        self.root.mainloop()


class SettingsWindow:
    """Окно настроек"""
    
    def __init__(self, parent, config):
        self.config = config
        self.window = tk.Toplevel(parent)
        self.window.title("Настройки")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_settings_gui()
    
    def setup_settings_gui(self):
        """Настройка GUI окна настроек"""
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Вкладка API
        api_frame = ttk.Frame(notebook, padding="10")
        notebook.add(api_frame, text="API")
        
        ttk.Label(api_frame, text="HikerAPI ключ:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.api_key_var = tk.StringVar(value=self.config.get('API', 'hiker_api_key'))
        
        # Фрейм для поля API ключа и кнопки показа
        api_key_frame = ttk.Frame(api_frame)
        api_key_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        api_key_frame.columnconfigure(0, weight=1)
        
        self.api_entry = ttk.Entry(api_key_frame, textvariable=self.api_key_var, width=35, show="*")
        self.api_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Добавляем поддержку контекстного меню для вставки
        self.setup_api_entry_context_menu()
        
        self.show_key_var = tk.BooleanVar()
        show_key_button = ttk.Checkbutton(api_key_frame, text="Показать",
                                         variable=self.show_key_var,
                                         command=self.toggle_api_key_visibility)
        show_key_button.grid(row=0, column=1, padx=(5, 0))

        # Кнопка для вставки из буфера обмена
        paste_button = ttk.Button(api_key_frame, text="Вставить",
                                 command=self.paste_to_api_entry, width=8)
        paste_button.grid(row=0, column=2, padx=(5, 0))
        
        # Подсказка для API ключа
        api_hint = ttk.Label(api_frame, text="💡 Получите ключ на hikerapi.com", 
                            font=('Arial', 8), foreground='gray')
        api_hint.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Кнопка тестирования API
        test_api_button = ttk.Button(api_frame, text="Тест API",
                                    command=self.test_api_key, width=10)
        test_api_button.grid(row=0, column=2, padx=(5, 0))

        # Кнопка для простого ввода API ключа
        simple_input_button = ttk.Button(api_frame, text="Простой ввод API",
                                        command=self.simple_api_input, width=15)
        simple_input_button.grid(row=2, column=1, pady=(10, 0), sticky=tk.W, padx=(10, 0))
        
        ttk.Label(api_frame, text="Базовый URL:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.base_url_var = tk.StringVar(value=self.config.get('API', 'base_url'))
        url_entry = ttk.Entry(api_frame, textvariable=self.base_url_var, width=40)
        url_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # Вкладка настроек
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="Настройки")
        
        ttk.Label(settings_frame, text="Время ежедневного запуска:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.daily_time_var = tk.StringVar(value=self.config.get('SETTINGS', 'daily_run_time'))
        time_entry = ttk.Entry(settings_frame, textvariable=self.daily_time_var, width=10)
        time_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(settings_frame, text="Задержка между запросами (сек):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.delay_var = tk.StringVar(value=self.config.get('SETTINGS', 'request_delay'))
        delay_entry = ttk.Entry(settings_frame, textvariable=self.delay_var, width=10)
        delay_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(settings_frame, text="Максимум попыток:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.retries_var = tk.StringVar(value=self.config.get('SETTINGS', 'max_retries'))
        retries_entry = ttk.Entry(settings_frame, textvariable=self.retries_var, width=10)
        retries_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Кнопки
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Сохранить", command=self.save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Отмена", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def setup_api_entry_context_menu(self):
        """Настройка контекстного меню для поля API ключа"""
        def show_context_menu(event):
            try:
                context_menu = tk.Menu(self.window, tearoff=0)
                context_menu.add_command(label="Вырезать", command=lambda: self.api_entry.event_generate("<<Cut>>"))
                context_menu.add_command(label="Копировать", command=lambda: self.api_entry.event_generate("<<Copy>>"))
                context_menu.add_command(label="Вставить", command=lambda: self.paste_to_api_entry())
                context_menu.add_separator()
                context_menu.add_command(label="Выделить всё", command=lambda: self.api_entry.select_range(0, tk.END))
                context_menu.add_command(label="Очистить", command=lambda: self.api_key_var.set(""))

                context_menu.tk_popup(event.x_root, event.y_root)
            except Exception as e:
                print(f"Ошибка контекстного меню: {e}")
            finally:
                try:
                    context_menu.destroy()
                except:
                    pass

        # Привязываем контекстное меню к правой кнопке мыши
        self.api_entry.bind("<Button-3>", show_context_menu)

        # Добавляем горячие клавиши
        self.api_entry.bind("<Control-v>", lambda e: self.paste_to_api_entry())
        self.api_entry.bind("<Control-a>", lambda e: self.api_entry.select_range(0, tk.END))

    def paste_to_api_entry(self):
        """Улучшенная вставка в поле API ключа"""
        try:
            # Получаем текст из буфера обмена
            clipboard_text = self.window.clipboard_get()

            # Очищаем поле и вставляем новый текст
            self.api_key_var.set(clipboard_text.strip())

            # Устанавливаем курсор в конец
            self.api_entry.icursor(tk.END)

            return "break"  # Предотвращаем стандартную обработку
        except tk.TclError:
            # Буфер обмена пуст или недоступен
            pass
        except Exception as e:
            print(f"Ошибка вставки: {e}")

    def toggle_api_key_visibility(self):
        """Переключение видимости API ключа"""
        if self.show_key_var.get():
            self.api_entry.config(show="")
        else:
            self.api_entry.config(show="*")
    
    def test_api_key(self):
        """Тестирование API ключа"""
        api_key = self.api_key_var.get().strip()
        if not api_key or api_key == "YOUR_HIKER_API_KEY_HERE":
            messagebox.showerror("Ошибка", "Введите API ключ для тестирования")
            return
        
        try:
            import requests
            base_url = self.base_url_var.get()

            # Простой тест API - пробуем получить информацию о пользователе (простой эндпоинт)
            test_url = f"{base_url}/user/by/id"
            headers = {
                "accept": "application/json",
                "x-access-key": api_key
            }
            params = {"id": "123123"}  # Тестовый ID пользователя

            response = requests.get(test_url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                messagebox.showinfo("Успех", "✅ API ключ работает корректно!")
            elif response.status_code == 401:
                messagebox.showerror("Ошибка", "❌ Неверный API ключ")
            elif response.status_code == 429:
                messagebox.showwarning("Предупреждение", "⚠️ Превышен лимит запросов, но ключ валидный")
            else:
                messagebox.showwarning("Предупреждение", f"⚠️ Получен ответ {response.status_code}. Возможно, ключ работает")
                
        except requests.exceptions.RequestException as e:
            messagebox.showerror("Ошибка", f"❌ Ошибка соединения: {str(e)}")
        except Exception as e:
            messagebox.showerror("Ошибка", f"❌ Ошибка тестирования: {str(e)}")

    def simple_api_input(self):
        """Простое окно для ввода API ключа"""
        # Создаем новое окно
        input_window = tk.Toplevel(self.window)
        input_window.title("Ввод API ключа")
        input_window.geometry("600x400")
        input_window.resizable(True, True)

        # Делаем окно модальным
        input_window.transient(self.window)
        input_window.grab_set()

        # Центрируем окно
        input_window.update_idletasks()
        x = (input_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (input_window.winfo_screenheight() // 2) - (400 // 2)
        input_window.geometry(f"600x400+{x}+{y}")

        # Основной фрейм
        main_frame = ttk.Frame(input_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Заголовок
        title_label = ttk.Label(main_frame, text="Ввод API ключа HikerAPI",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # Инструкция
        instruction_text = """1. Скопируйте ваш API ключ с сайта hikerapi.com
2. Вставьте его в поле ниже (Ctrl+V работает!)
3. Нажмите "Сохранить и проверить" """

        instruction_label = ttk.Label(main_frame, text=instruction_text,
                                     font=('Arial', 10), justify=tk.LEFT)
        instruction_label.pack(pady=(0, 15))

        # Поле для ввода API ключа
        ttk.Label(main_frame, text="API ключ:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        # Используем обычный Text виджет вместо Entry для лучшей поддержки вставки
        api_text = tk.Text(main_frame, height=2, width=50, wrap=tk.WORD,
                          font=('Consolas', 10))
        api_text.pack(pady=(5, 10), fill=tk.X)

        # Вставляем текущий ключ если есть
        current_key = self.api_key_var.get()
        if current_key and current_key not in ["YOUR_HIKER_API_KEY_HERE", "PASTE_YOUR_REAL_API_KEY_HERE"]:
            api_text.insert("1.0", current_key)

        # Фокус на поле ввода
        api_text.focus_set()

        # Фрейм для кнопок
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(10, 0))

        def save_and_test():
            """Сохранить ключ и протестировать"""
            api_key = api_text.get("1.0", tk.END).strip()

            if not api_key:
                messagebox.showerror("Ошибка", "Введите API ключ!")
                return

            # Сохраняем ключ
            self.api_key_var.set(api_key)

            # Закрываем окно
            input_window.destroy()

            # Тестируем ключ
            self.test_api_key()

        def just_save():
            """Просто сохранить без тестирования"""
            api_key = api_text.get("1.0", tk.END).strip()

            if not api_key:
                messagebox.showerror("Ошибка", "Введите API ключ!")
                return

            # Сохраняем ключ
            self.api_key_var.set(api_key)

            # Закрываем окно
            input_window.destroy()

            messagebox.showinfo("Успех", "API ключ сохранен!")

        # Кнопки (размещаем вертикально для лучшего размещения)
        ttk.Button(button_frame, text="Сохранить и проверить",
                  command=save_and_test, width=25).pack(pady=(0, 5))
        ttk.Button(button_frame, text="Просто сохранить",
                  command=just_save, width=25).pack(pady=(0, 5))
        ttk.Button(button_frame, text="Отмена",
                  command=input_window.destroy, width=25).pack(pady=(0, 5))

        # Подсказка
        hint_label = ttk.Label(main_frame,
                              text="💡 Совет: Ctrl+A выделит весь текст, Ctrl+V вставит из буфера",
                              font=('Arial', 8), foreground='gray')
        hint_label.pack(pady=(15, 0))

    def save_settings(self):
        """Сохранение настроек"""
        try:
            self.config.set('API', 'hiker_api_key', self.api_key_var.get())
            self.config.set('API', 'base_url', self.base_url_var.get())
            self.config.set('SETTINGS', 'daily_run_time', self.daily_time_var.get())
            self.config.set('SETTINGS', 'request_delay', self.delay_var.get())
            self.config.set('SETTINGS', 'max_retries', self.retries_var.get())
            
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            messagebox.showinfo("Успех", "Настройки сохранены успешно!")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка сохранения настроек: {str(e)}")


def main():
    """Главная функция"""
    try:
        app = InstagramReelsGUI()
        app.run()
    except Exception as e:
        print(f"Критическая ошибка запуска: {e}")
        logging.error(f"Критическая ошибка запуска: {e}\n{traceback.format_exc()}")


if __name__ == "__main__":
    main() 