2025-06-27 18:48:44,223 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-06-27 19:41:32,266 - ERROR - Критическая ошибка запуска: 'InstagramReelsGUI' object has no attribute 'enable_windows_autostart'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 757, in main
    app = InstagramReelsGUI()
          ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 406, in __init__
    self.setup_gui()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 465, in setup_gui
    command=self.enable_windows_autostart)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'InstagramReelsGUI' object has no attribute 'enable_windows_autostart'

2025-06-27 19:49:17,874 - ERROR - Критическая ошибка запуска: unknown option "-background"
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 850, in main
    app = InstagramReelsGUI()
          ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 407, in __init__
    self.setup_gui()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 451, in setup_gui
    self.autostart_status_color = tk.Canvas(autostart_frame, width=18, height=18, highlightthickness=0, bg=autostart_frame.cget('background'))
                                                                                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\tkinter\__init__.py", line 1708, in cget
    return self.tk.call(self._w, 'cget', '-' + key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown option "-background"

2025-06-27 19:51:12,735 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-06-27 19:58:51,103 - ERROR - Критическая ошибка запуска: unknown option "-background"
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 921, in main
    app = InstagramReelsGUI()
          ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 464, in __init__
    self.setup_gui()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 517, in setup_gui
    self.autostart_toggle = ToggleSwitch(
                            ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 36, in __init__
    super().__init__(master, width=width, height=height, highlightthickness=0, bg=master.cget('background'), bd=0, **kwargs)
                                                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\tkinter\__init__.py", line 1708, in cget
    return self.tk.call(self._w, 'cget', '-' + key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown option "-background"

2025-06-30 12:38:29,017 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-06-30 12:39:42,304 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-07-01 11:42:26,136 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-07-01 11:58:23,309 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:23,626 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:25,385 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:25,784 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:28,485 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:28,777 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:31,475 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:31,842 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:33,432 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:33,762 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:36,474 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:36,842 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:39,596 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:40,036 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:41,680 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:42,104 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:44,971 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:45,251 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKH8AqyRNoT: Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:47,394 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-07-01 12:07:38,005 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:38,448 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:40,229 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:40,536 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:43,313 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:43,664 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:46,418 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:46,769 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:48,576 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:48,914 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:51,558 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:52,083 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:07:54,151 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-07-01 12:12:18,413 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:18,773 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:20,512 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:21,029 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:23,697 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:24,053 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/v1/instagram/post/DKjo16YqYSo: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:26,689 - WARNING - Ошибка запроса к https://hikerapi.com/v1/instagram/media_info?shortcode=DKUE5j_o89M: Expecting value: line 1 column 1 (char 0)
2025-07-01 12:12:49,724 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKjo16YqYSo
2025-07-01 12:12:50,174 - WARNING - Reel DKjo16YqYSo не найден (404)
2025-07-01 12:12:52,188 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 12:12:52,492 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 12:12:54,504 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKH8AqyRNoT
2025-07-01 12:12:54,813 - WARNING - Reel DKH8AqyRNoT не найден (404)
2025-07-01 12:12:56,821 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKF6ZqxQMnS
2025-07-01 12:12:57,179 - WARNING - Reel DKF6ZqxQMnS не найден (404)
2025-07-01 12:12:59,360 - INFO - Парсинг завершен. Обработано: 5, Успешно: 0
2025-07-01 12:36:12,885 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DLE0bPai8iW
2025-07-01 12:36:13,287 - WARNING - Reel DLE0bPai8iW не найден (404)
2025-07-01 12:36:15,296 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKl9t3GoC6I
2025-07-01 12:36:15,611 - WARNING - Reel DKl9t3GoC6I не найден (404)
2025-07-01 12:36:17,624 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DLEjpTGIUHS
2025-07-01 12:36:18,029 - WARNING - Reel DLEjpTGIUHS не найден (404)
2025-07-01 12:36:20,040 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKkMZLKNRXf
2025-07-01 12:36:20,663 - WARNING - Reel DKkMZLKNRXf не найден (404)
2025-07-01 12:36:22,759 - ERROR - Критическая ошибка при парсинге: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 441, in parse_and_save
    df.to_excel(excel_path, index=False)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\generic.py", line 2436, in to_excel
    formatter.write(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\formats\excel.py", line 943, in write
    writer = ExcelWriter(
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_xlsxwriter.py", line 204, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_base.py", line 1246, in __init__
    self._handles = get_handle(
                    ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\common.py", line 882, in get_handle
    handle = open(handle, ioargs.mode)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'

2025-07-01 12:37:50,492 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKjoJ6YoYso
2025-07-01 12:37:50,777 - WARNING - Reel DKjoJ6YoYso не найден (404)
2025-07-01 12:37:52,787 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 12:37:53,078 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 12:37:55,183 - ERROR - Критическая ошибка при парсинге: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 441, in parse_and_save
    df.to_excel(excel_path, index=False)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\generic.py", line 2436, in to_excel
    formatter.write(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\formats\excel.py", line 943, in write
    writer = ExcelWriter(
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_xlsxwriter.py", line 204, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_base.py", line 1246, in __init__
    self._handles = get_handle(
                    ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\common.py", line 882, in get_handle
    handle = open(handle, ioargs.mode)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'

2025-07-01 13:03:17,024 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKjoJ6YoYso
2025-07-01 13:03:17,437 - WARNING - Reel DKjoJ6YoYso не найден (404)
2025-07-01 13:03:19,445 - INFO - Запрос к API: https://api.hikerapi.com/v2/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 13:03:19,948 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 13:03:22,034 - ERROR - Критическая ошибка при парсинге: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 441, in parse_and_save
    if progress_callback:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\generic.py", line 2436, in to_excel
    formatter.write(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\formats\excel.py", line 943, in write
    writer = ExcelWriter(
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_xlsxwriter.py", line 204, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\excel\_base.py", line 1246, in __init__
    self._handles = get_handle(
                    ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\io\common.py", line 882, in get_handle
    handle = open(handle, ioargs.mode)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format.xlsx'

2025-07-01 13:23:58,453 - ERROR - Критическая ошибка запуска: 'InstagramReelsGUI' object has no attribute 'log_text'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 1131, in main
    app = InstagramReelsGUI()
          ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 575, in __init__
    self.setup_gui()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 687, in setup_gui
    self.log_message(f"📁 Загружен последний открытый файл: {self.last_file_path}")
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 980, in log_message
    update_log()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 976, in update_log
    self.log_text.insert(tk.END, formatted_message)
    ^^^^^^^^^^^^^
AttributeError: 'InstagramReelsGUI' object has no attribute 'log_text'

2025-07-01 13:40:12,325 - ERROR - Критическая ошибка запуска: 'InstagramReelsGUI' object has no attribute 'log_text'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 1178, in main
    app = InstagramReelsGUI()
          ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 577, in __init__
    self.setup_gui()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 734, in setup_gui
    self.log_message(f"📁 Загружен последний открытый файл: {self.last_file_path}")
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 1027, in log_message
    update_log()
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 1023, in update_log
    self.log_text.insert(tk.END, formatted_message)
    ^^^^^^^^^^^^^
AttributeError: 'InstagramReelsGUI' object has no attribute 'log_text'

2025-07-01 13:52:41,238 - ERROR - API access key не настроен в config.ini
2025-07-01 13:52:43,246 - ERROR - API access key не настроен в config.ini
2025-07-01 13:52:45,353 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 13:56:12,081 - ERROR - Непредвиденная ошибка: cannot access local variable 'endpoint' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 216, in fetch_reel_data
    self.logger.info(f"Запрос к API: {endpoint}?shortcode={shortcode}")
                                      ^^^^^^^^
UnboundLocalError: cannot access local variable 'endpoint' where it is not associated with a value
2025-07-01 13:56:14,090 - ERROR - Непредвиденная ошибка: cannot access local variable 'endpoint' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Воркзилла\Парсинг инстаграмм\Сервис с гуи\instagram_reels_parser.py", line 216, in fetch_reel_data
    self.logger.info(f"Запрос к API: {endpoint}?shortcode={shortcode}")
                                      ^^^^^^^^
UnboundLocalError: cannot access local variable 'endpoint' where it is not associated with a value
2025-07-01 13:56:16,198 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 14:02:11,538 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso?shortcode=DKjoJ6YoYso
2025-07-01 14:02:11,539 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso
2025-07-01 14:02:12,137 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:02:14,148 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M?shortcode=DKUE5j_o89M
2025-07-01 14:02:14,149 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M
2025-07-01 14:02:14,493 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:02:16,619 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 14:02:43,822 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso?shortcode=DKjoJ6YoYso
2025-07-01 14:02:43,822 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso
2025-07-01 14:02:44,167 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:02:46,171 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M?shortcode=DKUE5j_o89M
2025-07-01 14:02:46,171 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M
2025-07-01 14:02:46,545 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:02:48,573 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 14:03:26,071 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso?shortcode=DKjoJ6YoYso
2025-07-01 14:03:26,071 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKjoJ6YoYso
2025-07-01 14:03:26,315 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:03:28,323 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M?shortcode=DKUE5j_o89M
2025-07-01 14:03:28,324 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/DKUE5j_o89M
2025-07-01 14:03:28,632 - ERROR - Ошибка авторизации: неверный API access key
2025-07-01 14:03:30,693 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 14:08:36,112 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKjoJ6YoYso
2025-07-01 14:08:36,112 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:08:36,500 - WARNING - Reel DKjoJ6YoYso не найден (404)
2025-07-01 14:08:38,519 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 14:08:38,520 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:08:38,893 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 14:08:41,024 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
2025-07-01 14:14:19,045 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 14:14:19,045 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:14:19,444 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 14:14:21,453 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKsdIuNxm-V
2025-07-01 14:14:21,453 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:14:21,950 - WARNING - Reel DKsdIuNxm-V не найден (404)
2025-07-01 14:14:24,034 - ERROR - Не удалось сохранить файл. Возможно, он открыт в другой программе: [WinError 32] Процесс не может получить доступ к файлу, так как этот файл занят другим процессом: 'C:/Users/<USER>/Documents/Воркзилла/Парсинг инстаграмм/Сервис с гуи/test_reels_format_cleaned.xlsx'
2025-07-01 14:14:44,826 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKUE5j_o89M
2025-07-01 14:14:44,826 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:14:45,108 - WARNING - Reel DKUE5j_o89M не найден (404)
2025-07-01 14:14:47,130 - INFO - Запрос к API: https://api.hikerapi.com/v2/instagram/media/by/shortcode?shortcode=DKsdIuNxm-V
2025-07-01 14:14:47,131 - INFO - Попытка запроса к эндпоинту: https://api.hikerapi.com/v2/instagram/media/by/shortcode
2025-07-01 14:14:47,469 - WARNING - Reel DKsdIuNxm-V не найден (404)
2025-07-01 14:14:49,658 - INFO - Парсинг завершен. Обработано: 2, Успешно: 0
